from termcolor import colored 
import numpy as np
import cv2 #pip install opencv-python
import win32gui, win32ui, win32con
import torch
import time
import keyboard
import pathlib
from pathlib import Path
import ctypes
import sys
import os
sys.path.insert(0, './yolov5') # تأكد أن مجلد yolov5 موجود في نفس مسار ملف hok.py
from models.common import DetectMultiBackend

pathlib.PosixPath = pathlib.WindowsPath
print(colored(r'''     _    _     _
    | |  / /_ _| |   ___   __ _ _
    | | / / _` | |  / _ \ / _` | |
    | |/ / (_| | |_| (_) | (_| | |
    |___/ \__,_|____\___/ \__,_|_|
 
https://discord.gg/bnq2msQZD2''', "magenta", attrs=['bold']))
fov = 320
mid = fov / 2
height = int((1920 / 2) - mid)
width = int((1080 / 2) - mid)

# تعريف الهياكل Constants and Structures for SendInput
PUL = ctypes.POINTER(ctypes.c_ulong)

class KeyBdInput(ctypes.Structure):
    _fields_ = [("wVk", ctypes.c_ushort),
                ("wScan", ctypes.c_ushort),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", PUL)]

class HardwareInput(ctypes.Structure):
    _fields_ = [("uMsg", ctypes.c_ulong),
                ("wParamL", ctypes.c_short),
                ("wParamH", ctypes.c_short)]

class MouseInput(ctypes.Structure):
    _fields_ = [("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", ctypes.c_ulong),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", PUL)]

class Input_I(ctypes.Union):
    _fields_ = [("ki", KeyBdInput),
                ("mi", MouseInput),
                ("hi", HardwareInput)]

class Input(ctypes.Structure):
    _fields_ = [("type", ctypes.c_ulong),
                ("ii", Input_I)]

# تعريف الثوابت Constants
INPUT_MOUSE = 0
MOUSEEVENTF_MOVE = 0x0001
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004
MOUSEEVENTF_ABSOLUTE = 0x8000 # نستخدم الحركة النسبية هنا، لذا لا نستخدم هذا العلم

# تعريف وظيفة SendInput من مكتبة user32.dll
SendInput = ctypes.windll.user32.SendInput

def move_mouse_relative_sendinput(dx, dy):
    """Moves the mouse cursor by a relative amount (dx, dy) using SendInput."""
    mi = MouseInput(dx=dx, dy=dy, mouseData=0, dwFlags=MOUSEEVENTF_MOVE, time=0, dwExtraInfo=None)
    ii = Input_I(mi=mi)
    input_obj = Input(type=INPUT_MOUSE, ii=ii)
    
    num_inputs = 1
    cbSize = ctypes.sizeof(Input)
    
    if SendInput(num_inputs, ctypes.byref(input_obj), cbSize) == 0:
        return False
    return True

def left_click_sendinput():
    """Simulates a left mouse click using SendInput."""
    mi_down = MouseInput(dx=0, dy=0, mouseData=0, dwFlags=MOUSEEVENTF_LEFTDOWN, time=0, dwExtraInfo=None)
    ii_down = Input_I(mi=mi_down)
    input_down = Input(type=INPUT_MOUSE, ii=ii_down)

    mi_up = MouseInput(dx=0, dy=0, mouseData=0, dwFlags=MOUSEEVENTF_LEFTUP, time=0, dwExtraInfo=None)
    ii_up = Input_I(mi=mi_up)
    input_up = Input(type=INPUT_MOUSE, ii=ii_up)
    
    inputs = (Input * 2)(input_down, input_up) # Array of two Input structures

    num_inputs = 2
    cbSize = ctypes.sizeof(Input)

    if SendInput(num_inputs, inputs, cbSize) == 0:
         return False
    return True

# مسار ملف النموذج المخصص (اختر بين .onnx أو .trt)
# تأكد أن الملف موجود في نفس مجلد hok.py
custom_model_path = './Apex.onnx' # Changed model path back to Apex.onnx
# custom_model_path = './Apex_FP16.engine'
# custom_model_path = './yolov5s.pt'

# تحديد الجهاز المستخدم
device = 'cuda' if torch.cuda.is_available() else 'cpu'
if device == 'cuda':
    print(colored("CUDA ACCELERATION [ENABLED]", "green"))
else:
    print(colored("Running on CPU", "yellow"))

# تحميل النموذج المخصص باستخدام DetectMultiBackend
# DetectMultiBackend تتعامل مع مختلف الصيغ (pt, onnx, trt, engine, coreml, saved_model, pb, tflite, json, yaml)
try:
    model = DetectMultiBackend(custom_model_path, device=torch.device(device), fuse=True)
    # إذا كنت تستخدم TensorRT (.trt)، قد تحتاج لإعدادات إضافية لنظامك
except Exception as e:
    print(colored(f"Error loading custom model from {custom_model_path}: {e}", "red"))
    print(colored("Please ensure the model file exists and is compatible with DetectMultiBackend.", "red"))
    # يمكنك هنا الخروج من البرنامج أو تحميل نموذج افتراضي كحل بديل
    exit()

def calculatedistance(x, y):
    print(f"Moving mouse by x={x}, y={y} using SendInput")
    # استخدم int(x) و int(y) لتحويل القيم إلى أعداد صحيحة
    # قد تحتاج لتقريب القيم أو استخدام أساليب تحويل أخرى حسب دقة الحركة المطلوبة
    success_move = move_mouse_relative_sendinput(int(x), int(y))
    if success_move:
         print("Mouse move command sent.")
         # إذا كنت تريد النقر بعد الحركة، استدعي دالة النقر
         # time.sleep(0.01) # قد تحتاج لانتظار قصير جداً بين الحركة والنقر
         # success_click = left_click_sendinput()
         # if success_click:
         #      print("Left click command sent.")
         # else:
         #      print("Left click command failed.")
    else:
         print("Mouse move command failed.")

def windowcapture():
    hwnd = None
    wDC = win32gui.GetWindowDC(hwnd)
    dcObj = win32ui.CreateDCFromHandle(wDC)
    cDC = dcObj.CreateCompatibleDC()
    dataBitMap = win32ui.CreateBitmap()
    dataBitMap.CreateCompatibleBitmap(dcObj, fov, fov)
    cDC.SelectObject(dataBitMap)
    cDC.BitBlt((0, 0), (fov, fov), dcObj, (height, width), win32con.SRCCOPY)
    signedIntsArray = dataBitMap.GetBitmapBits(True)
    img = np.frombuffer(signedIntsArray, dtype='uint8').reshape((fov, fov, 4))
    dcObj.DeleteDC()
    cDC.DeleteDC()
    win32gui.ReleaseDC(hwnd, wDC)
    win32gui.DeleteObject(dataBitMap.GetHandle())
    img = cv2.cvtColor(img, cv2.COLOR_BGRA2BGR)
    return img

def preprocess_image(img):
    # Resize image to model's expected input size
    img = cv2.resize(img, (640, 640))
    # Convert to float and normalize
    img = img.astype(np.float32) / 255.0
    # Add batch dimension and transpose to (batch, channels, height, width)
    img = np.transpose(img, (2, 0, 1))
    img = np.expand_dims(img, 0)
    # Convert to torch tensor
    img = torch.from_numpy(img)
    return img

print('Running! Press Q to quit (for debugging only)')
while True:
    sct_img = windowcapture()
    # Preprocess the image before passing to model
    processed_img = preprocess_image(sct_img)
    
    # --- Debugging: Check processed image format ---
    print(f"Processed image type: {processed_img.dtype}")
    print(f"Processed image shape: {processed_img.shape}")
    # ----------------------------------------------
    
    results = model(processed_img)
    
    # --- Debugging: Check model output ---
    print(f"Model results type: {type(results)}")
    if isinstance(results, torch.Tensor):
        print(f"Model results shape: {results.shape}")
    # -------------------------------------

    # Handle ONNX model output
    detection_tensor = None # Initialize detection_tensor
    if isinstance(results, list):
        # Iterate through the list to find the detection tensor
        # Looking for a 3D tensor with the last dimension being 8 (common for YOLO raw output)
        for res in results:
            if isinstance(res, torch.Tensor) and res.ndim == 3 and res.shape[2] == 8:
                 # Further check if the second dimension is large, like 25200
                if res.shape[1] > 100: # Use a threshold to identify the detection tensor
                    detection_tensor = res
                    break # Found the likely detection tensor

    # Process detections if the detection tensor was found
    detections_found = False
    if detection_tensor is not None:
        # Convert results tensor to numpy array
        results_np = detection_tensor.cpu().numpy()

        # The results_np shape is typically (batch_size, num_predictions, 8)
        # where each prediction is [x1, y1, x2, y2, confidence, class_score_1, class_score_2, ...]

        # Assuming batch size is 1
        predictions = results_np[0]

        # Confidence threshold (keeping it low to find class index)
        confidence_threshold = 0.60

        # Filter and process detections
        print("Filtered Detections (Conf > 0.05):")
        for prediction in predictions:
            conf = prediction[4] # Confidence score is at index 4

            if conf > confidence_threshold:
                detections_found = True
                xmin, ymin, xmax, ymax = prediction[:4] # Bbox coordinates
                class_scores = prediction[5:] # Class scores
                predicted_class_index = np.argmax(class_scores)
                predicted_class_score = class_scores[predicted_class_index]

                print(f"  Conf: {conf:.2f}, Predicted Class Index: {predicted_class_index}, Class Score: {predicted_class_score:.2f}")

                # --- Drawing and Aiming Logic (applies to the first suitable detection) ---
                # In a real scenario with multiple objects, you'd loop or select the closest/most central
                # For now, we will draw and aim at the first detection that passes the filter
                # To handle multiple objects, you would need to decide which one to aim at (e.g., closest, highest confidence of target class)
                # For debugging class index, we will print all, but only draw/aim at the first one found

                # Scale coordinates back to original image size (fov x fov)
                scale_x = sct_img.shape[1] / 640.0
                scale_y = sct_img.shape[0] / 640.0

                xmin_scaled = int(xmin * scale_x)
                ymin_scaled = int(ymin * scale_y)
                xmax_scaled = int(xmax * scale_x)
                ymax_scaled = int(ymax * scale_y)

                cX = (xmin_scaled + xmax_scaled) / 2
                cY = (ymin_scaled + ymax_scaled) / 2

                # Calculate relative position from the center of the captured fov
                mid = fov / 2
                x = cX - mid

                # Adjust y-coordinate to aim at the upper chest (subtract offset from cY)
                aim_offset_y = 75 # Keeping aim offset at 75
                adjusted_cY = cY - aim_offset_y
                y = adjusted_cY - mid

                # Draw bounding box and center point on the original sct_img (fov x fov)
                cv2.rectangle(sct_img, (xmin_scaled, ymin_scaled), (xmax_scaled, ymax_scaled), (0, 255, 0), 2)
                cv2.circle(sct_img, (int(cX), int(cY)), 5, (0, 0, 255), -1)
                # Display confidence score and class index near the bounding box
                cv2.putText(sct_img, f"Conf: {conf:.2f}", (xmin_scaled, ymin_scaled - 25),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
                cv2.putText(sct_img, f"Class: {predicted_class_index}", (xmin_scaled, ymin_scaled - 10),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 2)
                # Draw the adjusted aim point
                cv2.circle(sct_img, (int(cX), int(adjusted_cY)), 5, (255, 255, 0), -1) # Yellow circle for aim point

                if keyboard.is_pressed('Alt'):
                    calculatedistance(x, y)

                # --- Stop processing after the first found detection for now ---
                # To process all detections, remove this break and modify drawing/aiming logic
                break # Process only the first detection that meets the confidence threshold
                # ------------------------------------------------------------------

    # Optional: Add a message if no detections are found
    # if not detections_found:
    #     print("No objects detected.") # Or log this less frequently
    #     pass # Keep it silent if no objects are found to avoid spamming console

    cv2.imshow("VALOAI", sct_img)
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break
cv2.destroyAllWindows()