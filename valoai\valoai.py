from termcolor import colored
import numpy as np
import win32gui, win32ui, win32con
import torch
import time
import keyboard
import pathlib
from pathlib import Path
import ctypes
import warnings
# Suppress FutureWarnings from YOLOv5
warnings.filterwarnings("ignore", category=FutureWarning)
pathlib.PosixPath = pathlib.WindowsPath
print(colored(r'''     _    _     _
    | |  / /_ _| |   ___   __ _ _
    | | / / _` | |  / _ \ / _` | |
    | |/ / (_| | |_| (_) | (_| | |
    |___/ \__,_|____\___/ \__,_|_|''', "magenta",attrs=['bold']))
fov = 320
mid = fov / 2
height = int((1920 / 2) - mid)
width = int((1080 / 2) - mid)

# تعريف الهياكل Constants and Structures for SendInput
PUL = ctypes.POINTER(ctypes.c_ulong)

class KeyBdInput(ctypes.Structure):
    _fields_ = [("wVk", ctypes.c_ushort),
                ("wScan", ctypes.c_ushort),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", PUL)]

class HardwareInput(ctypes.Structure):
    _fields_ = [("uMsg", ctypes.c_ulong),
                ("wParamL", ctypes.c_short),
                ("wParamH", ctypes.c_short)]

class MouseInput(ctypes.Structure):
    _fields_ = [("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", ctypes.c_ulong),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", PUL)]

class Input_I(ctypes.Union):
    _fields_ = [("ki", KeyBdInput),
                ("mi", MouseInput),
                ("hi", HardwareInput)]

class Input(ctypes.Structure):
    _fields_ = [("type", ctypes.c_ulong),
                ("ii", Input_I)]

# تعريف الثوابت Constants
INPUT_MOUSE = 0
MOUSEEVENTF_MOVE = 0x0001
MOUSEEVENTF_LEFTDOWN = 0x0002
MOUSEEVENTF_LEFTUP = 0x0004
MOUSEEVENTF_ABSOLUTE = 0x8000 # نستخدم الحركة النسبية هنا، لذا لا نستخدم هذا العلم

# تعريف وظيفة SendInput من مكتبة user32.dll
SendInput = ctypes.windll.user32.SendInput

def move_mouse_relative_sendinput(dx, dy):
    """Moves the mouse cursor by a relative amount (dx, dy) using SendInput."""
    mi = MouseInput(dx=dx, dy=dy, mouseData=0, dwFlags=MOUSEEVENTF_MOVE, time=0, dwExtraInfo=None)
    ii = Input_I(mi=mi)
    input_obj = Input(type=INPUT_MOUSE, ii=ii)

    num_inputs = 1
    cbSize = ctypes.sizeof(Input)

    if SendInput(num_inputs, ctypes.byref(input_obj), cbSize) == 0:
        return False
    return True

def left_click_sendinput():
    """Simulates a left mouse click using SendInput."""
    mi_down = MouseInput(dx=0, dy=0, mouseData=0, dwFlags=MOUSEEVENTF_LEFTDOWN, time=0, dwExtraInfo=None)
    ii_down = Input_I(mi=mi_down)
    input_down = Input(type=INPUT_MOUSE, ii=ii_down)

    mi_up = MouseInput(dx=0, dy=0, mouseData=0, dwFlags=MOUSEEVENTF_LEFTUP, time=0, dwExtraInfo=None)
    ii_up = Input_I(mi=mi_up)
    input_up = Input(type=INPUT_MOUSE, ii=ii_up)

    inputs = (Input * 2)(input_down, input_up) # Array of two Input structures

    num_inputs = 2
    cbSize = ctypes.sizeof(Input)

    if SendInput(num_inputs, inputs, cbSize) == 0:
         return False
    return True

#Replace 1920x1080 for a custom resolution
model = torch.hub.load('v/scripts/yolov5-master', 'custom', path='v/scripts/best640.pt', source='local', force_reload=True)
if torch.cuda.is_available():
    model = model.cuda()
    print(colored("CUDA ACCELERATION [ENABLED]", "green"))
else:
    print(colored("Running on CPU", "yellow"))
def calculatedistance(x, y):
    print(f"Moving mouse by x={x}, y={y} using SendInput")
    # استخدم int(x) و int(y) لتحويل القيم إلى أعداد صحيحة
    # قد تحتاج لتقريب القيم أو استخدام أساليب تحويل أخرى حسب دقة الحركة المطلوبة
    success_move = move_mouse_relative_sendinput(int(x), int(y))
    if success_move:
         print("Mouse move command sent.")
         # إذا كنت تريد النقر بعد الحركة، استدعي دالة النقر
         # time.sleep(0.01) # قد تحتاج لانتظار قصير جداً بين الحركة والنقر
         # success_click = left_click_sendinput()
         # if success_click:
         #      print("Left click command sent.")
         # else:
         #      print("Left click command failed.")
    else:
         print("Mouse move command failed.")
def windowcapture():
    hwnd = None
    wDC = win32gui.GetWindowDC(hwnd)
    dcObj = win32ui.CreateDCFromHandle(wDC)
    cDC = dcObj.CreateCompatibleDC()
    dataBitMap = win32ui.CreateBitmap()
    dataBitMap.CreateCompatibleBitmap(dcObj, fov, fov)
    cDC.SelectObject(dataBitMap)
    cDC.BitBlt((0, 0), (fov, fov), dcObj, (height, width), win32con.SRCCOPY)
    signedIntsArray = dataBitMap.GetBitmapBits(True)
    img = np.frombuffer(signedIntsArray, dtype='uint8').reshape((fov, fov, 4))
    dcObj.DeleteDC()
    cDC.DeleteDC()
    win32gui.ReleaseDC(hwnd, wDC)
    win32gui.DeleteObject(dataBitMap.GetHandle())
    return img
print('Running!')
while True:
    sct_img = windowcapture()
    results = model(sct_img, size=320)
    df = results.pandas().xyxy[0]
    if not df.empty:
        xmin, ymin, xmax, ymax = df.iloc[0, :4]
        cX = (xmin + xmax) / 2
        cY = (ymin + ymax) / 2
        x = cX - mid if cX > mid else -(mid - cX)
        y = cY - mid if cY > mid else -(mid - cY)
        if keyboard.is_pressed('Alt'):
            calculatedistance(x, y)
