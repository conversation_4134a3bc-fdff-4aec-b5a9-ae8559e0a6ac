# Ultralytics YOLOv5 🚀, AGPL-3.0 license

name: Close stale issues
on:
  schedule:
    - cron: "0 0 * * *" # Runs at 00:00 UTC every day

jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v9
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}

          stale-issue-message: |
            👋 Hello there! We wanted to give you a friendly reminder that this issue has not had any recent activity and may be closed soon, but don't worry - you can always reopen it if needed. If you still have any questions or concerns, please feel free to let us know how we can help.

            For additional resources and information, please see the links below:

            - **Docs**: https://docs.ultralytics.com
            - **HUB**: https://hub.ultralytics.com
            - **Community**: https://community.ultralytics.com

            Feel free to inform us of any other **issues** you discover or **feature requests** that come to mind in the future. Pull Requests (PRs) are also always welcomed!

            Thank you for your contributions to YOLO 🚀 and Vision AI ⭐

          stale-pr-message: |
            👋 Hello there! We wanted to let you know that we've decided to close this pull request due to inactivity. We appreciate the effort you put into contributing to our project, but unfortunately, not all contributions are suitable or aligned with our product roadmap.

            We hope you understand our decision, and please don't let it discourage you from contributing to open source projects in the future. We value all of our community members and their contributions, and we encourage you to keep exploring new projects and ways to get involved.

            For additional resources and information, please see the links below:

            - **Docs**: https://docs.ultralytics.com
            - **HUB**: https://hub.ultralytics.com
            - **Community**: https://community.ultralytics.com

            Thank you for your contributions to YOLO 🚀 and Vision AI ⭐

          days-before-issue-stale: 30
          days-before-issue-close: 10
          days-before-pr-stale: 90
          days-before-pr-close: 30
          exempt-issue-labels: "documentation,tutorial,TODO"
          operations-per-run: 300 # The maximum number of operations per run, used to control rate limiting.
